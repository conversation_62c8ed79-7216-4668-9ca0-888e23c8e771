"use client";

import { motion } from 'framer-motion';

/**
 * Background - Handles layered background elements for Gig Hunt
 * Manages the sliding transition from menu to gameplay background
 */
const Background = ({
  backgroundType = 'menu', // 'menu' or 'gameplay'
  isTransitioning = false,
  className = ""
}) => {
  
  const basePath = '/Projects/Games/gh/background/';
  
  // Background elements for different screens
  const backgroundElements = {
    menu: {
      bg: `${basePath}BG.svg`,
      clouds: [`${basePath}cloud_01.svg`, `${basePath}cloud_02.svg`],
      trees: [`${basePath}tree_01.svg`],
      ground: `${basePath}Ground.svg`,
      grass: `${basePath}Grass.svg`
    },
    gameplay: {
      bg: `${basePath}BG.svg`,
      clouds: [`${basePath}cloud_01.svg`, `${basePath}cloud_02.svg`],
      trees: [`${basePath}tree_01.svg`],
      ground: `${basePath}Ground.svg`,
      grass: `${basePath}Grass.svg`
    }
  };

  const currentBg = backgroundElements[backgroundType];

  return (
    <div className={`absolute inset-0 w-full h-full ${className}`}>
      {/* Background sliding container */}
      <motion.div
        className="relative w-full h-full"
        initial={{ x: backgroundType === 'gameplay' ? '100%' : '0%' }}
        animate={{ 
          x: isTransitioning ? (backgroundType === 'gameplay' ? '0%' : '-100%') : '0%'
        }}
        transition={{ 
          duration: 1.2, 
          ease: [0.4, 0, 0.2, 1] // Custom easing for smooth slide
        }}
      >
        {/* Sky/Main Background Layer */}
        <div className="absolute inset-0 z-0">
          <img
            src={currentBg.bg}
            alt="Sky background"
            className="w-full h-full object-cover"
            style={{ imageRendering: 'pixelated' }}
          />
        </div>

        {/* Clouds Layer - Behind everything */}
        <div className="absolute inset-0 z-10">
          {currentBg.clouds.map((cloudSrc, index) => (
            <img
              key={`cloud-${index}`}
              src={cloudSrc}
              alt={`Cloud ${index + 1}`}
              className="absolute"
              style={{ 
                imageRendering: 'pixelated',
                // Position clouds across the sky
                left: `${20 + (index * 40)}%`,
                top: `${10 + (index * 5)}%`,
                width: '120px',
                height: 'auto'
              }}
            />
          ))}
        </div>

        {/* Trees Layer - Background elements */}
        <div className="absolute inset-0 z-20">
          {currentBg.trees.map((treeSrc, index) => (
            <img
              key={`tree-${index}`}
              src={treeSrc}
              alt={`Tree ${index + 1}`}
              className="absolute bottom-20"
              style={{ 
                imageRendering: 'pixelated',
                left: `${60 + (index * 30)}%`,
                width: '80px',
                height: 'auto'
              }}
            />
          ))}
        </div>

        {/* Ground Layer */}
        <div className="absolute bottom-0 left-0 right-0 z-30">
          <img
            src={currentBg.ground}
            alt="Ground"
            className="w-full h-auto"
            style={{ imageRendering: 'pixelated' }}
          />
        </div>

        {/* Grass Layer - This will be split for William's z-index management */}
        <div className="absolute bottom-0 left-0 right-0 z-40" id="grass-background">
          <img
            src={currentBg.grass}
            alt="Grass background"
            className="w-full h-auto"
            style={{ imageRendering: 'pixelated' }}
          />
        </div>

        {/* Foreground Grass Layer - William can go behind this when jumping */}
        <div className="absolute bottom-0 left-0 right-0 z-60" id="grass-foreground">
          <img
            src={currentBg.grass}
            alt="Grass foreground"
            className="w-full h-auto opacity-80"
            style={{ 
              imageRendering: 'pixelated',
              // Slightly offset or modify this layer to create depth
              filter: 'brightness(0.9)'
            }}
          />
        </div>
      </motion.div>
    </div>
  );
};

export default Background;
