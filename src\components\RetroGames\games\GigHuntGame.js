"use client";

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import MenuScreen from './gigHunt/screens/MenuScreen';
import GameplayScreen from './gigHunt/screens/GameplayScreen';

// Game states
const GAME_STATES = {
  MENU: 'menu',
  INSTRUCTIONS: 'instructions', 
  CHARACTER_SELECT: 'character_select',
  GAMEPLAY: 'gameplay',
  GAME_OVER: 'game_over'
};

const GigHuntGame = () => {
  const [gameState, setGameState] = useState(GAME_STATES.MENU);
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Handle state transitions with smooth animations
  const changeGameState = async (newState) => {
    setIsTransitioning(true);
    
    // Wait for exit animation
    await new Promise(resolve => setTimeout(resolve, 300));
    
    setGameState(newState);
    setIsTransitioning(false);
  };

  // Handle menu actions
  const handleMenuAction = (action) => {
    switch (action) {
      case 'play':
        changeGameState(GAME_STATES.CHARACTER_SELECT);
        break;
      case 'instructions':
        changeGameState(GAME_STATES.INSTRUCTIONS);
        break;
      default:
        break;
    }
  };

  // Handle going back to menu
  const handleBackToMenu = () => {
    changeGameState(GAME_STATES.MENU);
  };

  // Render current screen based on game state
  const renderCurrentScreen = () => {
    switch (gameState) {
      case GAME_STATES.MENU:
        return (
          <MenuScreen 
            onMenuAction={handleMenuAction}
            isTransitioning={isTransitioning}
          />
        );
      
      case GAME_STATES.GAMEPLAY:
        return (
          <GameplayScreen 
            onBackToMenu={handleBackToMenu}
            isTransitioning={isTransitioning}
          />
        );
      
      default:
        return (
          <MenuScreen 
            onMenuAction={handleMenuAction}
            isTransitioning={isTransitioning}
          />
        );
    }
  };

  return (
    <div className="w-full h-full bg-black relative overflow-hidden">
      {/* Game Container with CRT effect */}
      <div className="w-full h-full relative">
        <AnimatePresence mode="wait">
          <motion.div
            key={gameState}
            initial={{ opacity: 0 }}
            animate={{ opacity: isTransitioning ? 0 : 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="w-full h-full"
          >
            {renderCurrentScreen()}
          </motion.div>
        </AnimatePresence>
      </div>
    </div>
  );
};

export default GigHuntGame;
