"use client";

import { motion } from 'framer-motion';
import Background from '../components/Background';

/**
 * MenuScreen - Main menu for Gig Hunt game
 * Shows title, menu options, and menu background
 */
const MenuScreen = ({ onMenuAction, isTransitioning }) => {
  
  const menuItems = [
    { id: 'play', label: 'PLAY', action: 'play' },
    { id: 'instructions', label: 'INSTRUCTIONS', action: 'instructions' }
  ];

  const handleMenuClick = (action) => {
    if (!isTransitioning) {
      onMenuAction(action);
    }
  };

  return (
    <div className="relative w-full h-full overflow-hidden">
      {/* Background Layer */}
      <Background 
        backgroundType="menu"
        isTransitioning={isTransitioning}
      />

      {/* Menu Content Overlay */}
      <div className="relative z-50 w-full h-full flex flex-col items-center justify-center">
        
        {/* Game Title */}
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: isTransitioning ? 0 : 1, y: isTransitioning ? -50 : 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <h1
            className="text-6xl font-bold text-white mb-4 tracking-wider"
            style={{
              fontFamily: 'var(--font-retro)',
              textShadow: '4px 4px 0px #000, 2px 2px 0px rgba(0,0,0,0.8)',
              filter: 'drop-shadow(0 0 10px rgba(255,255,255,0.3))'
            }}
          >
            GIG HUNT
          </h1>
          
          <p
            className="text-xl text-gray-300 tracking-wide"
            style={{
              fontFamily: 'var(--font-retro)',
              textShadow: '2px 2px 0px #000'
            }}
          >
            Hunt the Knowledge, Land the Gig
          </p>
        </motion.div>

        {/* Menu Items */}
        <motion.div
          className="flex flex-col space-y-4"
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: isTransitioning ? 0 : 1, y: isTransitioning ? 50 : 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          {menuItems.map((item, index) => (
            <motion.button
              key={item.id}
              className="px-8 py-3 bg-transparent border-2 border-white text-white hover:bg-white hover:text-black transition-all duration-200 cursor-pointer"
              style={{
                fontFamily: 'var(--font-retro)',
                fontSize: '24px',
                letterSpacing: '2px',
                textShadow: '2px 2px 0px #000'
              }}
              whileHover={{ 
                scale: 1.05,
                boxShadow: '0 0 20px rgba(255,255,255,0.5)'
              }}
              whileTap={{ scale: 0.95 }}
              onClick={() => handleMenuClick(item.action)}
              disabled={isTransitioning}
              initial={{ opacity: 0, x: -50 }}
              animate={{ 
                opacity: isTransitioning ? 0 : 1, 
                x: isTransitioning ? -50 : 0 
              }}
              transition={{ 
                duration: 0.4, 
                delay: 0.6 + (index * 0.1) 
              }}
            >
              {item.label}
            </motion.button>
          ))}
        </motion.div>

        {/* Subtitle/Credits */}
        <motion.div
          className="absolute bottom-8 text-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: isTransitioning ? 0 : 0.7 }}
          transition={{ duration: 0.6, delay: 1.0 }}
        >
          <p
            className="text-sm text-gray-400 tracking-wide"
            style={{
              fontFamily: 'var(--font-retro)',
              textShadow: '1px 1px 0px #000'
            }}
          >
            A Retro Portfolio Experience
          </p>
        </motion.div>
      </div>

      {/* Retro Scanlines Effect (Optional) */}
      <div 
        className="absolute inset-0 pointer-events-none z-40"
        style={{
          background: `repeating-linear-gradient(
            0deg,
            transparent,
            transparent 2px,
            rgba(0,0,0,0.1) 2px,
            rgba(0,0,0,0.1) 4px
          )`,
          opacity: 0.3
        }}
      />
    </div>
  );
};

export default MenuScreen;
